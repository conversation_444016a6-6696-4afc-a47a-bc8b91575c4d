<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class InvoiceEmail extends Mailable
{
    use Queueable, SerializesModels;

    public $pdfPath;
    public $invoiceNumber;
    public $paymentDeadline;
    public $customerName;
    public $packageTitle;

    /**
     * Create a new message instance.
     */
    public function __construct($pdfPath, $invoiceNumber, $paymentDeadline, $customerName, $packageTitle = null)
    {
        $this->pdfPath = $pdfPath;
        $this->invoiceNumber = $invoiceNumber;
        $this->paymentDeadline = $paymentDeadline;
        $this->customerName = $customerName;
        $this->packageTitle = $packageTitle;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from('<EMAIL>', 'Business Eagles')
                    ->replyTo('<EMAIL>', 'Business Eagles')
                    ->subject('Invoice ' . $this->invoiceNumber . ' - Business Eagles')
                    ->view('emails.invoice')
                    ->with([
                        'customerName' => $this->customerName,
                        'invoiceNumber' => $this->invoiceNumber,
                        'paymentDeadline' => $this->paymentDeadline,
                        'packageTitle' => $this->packageTitle,
                    ])
                    ->attach($this->pdfPath, [
                        'as' => 'Invoice-' . $this->invoiceNumber . '.pdf',
                        'mime' => 'application/pdf',
                    ]);
    }
}
