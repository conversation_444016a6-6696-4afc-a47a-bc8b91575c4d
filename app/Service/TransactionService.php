<?php

namespace App\Service;

use App\Models\Cart;
use App\Models\CourseLocation;
use App\Models\PromoCode;
use App\Mail\InvoiceEmail;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Exception;

class TransactionService
{
    public function getCart($user)
    {
        $cookieId = Cookie::get('business_eagles_cart');

        return Cart::query()
            ->where(fn($query) =>
                $query->where('user_id', $user->id)
                    ->when($cookieId, fn($q) => $q->orWhere('cookie_id', $cookieId)))
            ->whereHas('courseLocation')
            ->with(['courseLocation', 'courseLocation.course', 'courseLocation.location'])
            ->get();
    }

    public function generateVatNumber()
    {
        $prefix = 'GBP';
        $group1 = str_pad(rand(100, 999), 3, '0', STR_PAD_LEFT);
        $group2 = str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
        $group3 = str_pad(rand(1, 99), 2, '0', STR_PAD_LEFT);

        return "$prefix $group1 $group2 $group3";
    }

    public function handleInvoicePayment($user, $request, $cart)
    {
        $invoiceNumber = 'EIA' . date('Ymd') . strtoupper(Str::random(6));
        $paymentDeadline = Carbon::now()->addDays(7)->format('jS F Y');
        $vatNumber = $this->generateVatNumber();

        // Calculate final amount (use promo code if applied)
        $finalAmount = $request->final_amount ?? $request->subtotal_price;

        // Remove commas from final_amount if it's a string
        if (is_string($finalAmount)) {
            $finalAmount = (float) str_replace(',', '', $finalAmount);
        }
        $promoCode = $request->promo_code ?? null;
        $promoDiscountPercentage = $request->promo_discount_percentage ?? 0;
        $promoDiscountAmount = $request->promo_discount_amount ?? 0;

        $invoiceData = [
            'invoiceNumber' => $invoiceNumber,
            'name_surname' => $request->name,
            'job_title' => $request->job_title ?? '',
            'company' => $request->company ?? '',
            'address' => $request->address1,
            'email' => $request->email,
            'phone' => $request->mobile,
            'subtotal_price' => $request->subtotal_price,
            'vat_price' => $request->vat_price,
            'total_price' => $request->total_price,
            'final_amount' => $finalAmount,
            'promo_code' => $promoCode,
            'promo_discount_percentage' => $promoDiscountPercentage,
            'promo_discount_amount' => $promoDiscountAmount,
            'payment_deadline' => $paymentDeadline,
            'vat_number' => $vatNumber,
             'cart_items' => $cart
                ->unique('course_location_id')
                ->map(fn($item) => [
                    'course_location_id' => $item->course_location_id,
                    'name' => $item->courseLocation->name,
                    'price' => $item->courseLocation->price,
                    'quantity' => $item->qty,
                    'title' => $item->courseLocation->course->title,
                    'location' => $item->courseLocation->location->name,
                    'start_at' => $item->courseLocation->start_at->format('d M Y'),
                    'end_at' => $item->courseLocation->end_at->format('d M Y'),
                ])
                ->values(),
        ];

        $pdf = PDF::loadView('invoices.pdf', $invoiceData);
        $pdfPath = storage_path("app/invoices/{$invoiceNumber}.pdf");
        $pdf->save($pdfPath);
        $mails = [$request->email, '<EMAIL>'];
        foreach ($mails as $mail) {
            try {
                Mail::to($mail)->send(new InvoiceEmail(
                    $pdfPath,
                    $invoiceNumber,
                    $paymentDeadline,
                    $request->name
                ));
            } catch (Exception $e) {
                Log::error("Failed to send invoice email to {$mail}: " . $e->getMessage());
            }
        }

        $transaction = $user->transactions()->create([
            'name' => $request->name,
            'email' => $request->email,
            'mobile' => $request->mobile,
            'address1' => $request->address1,
            'address2' => $request->address2,
            'city' => $request->city,
            'zip_code' => $request->zip_code,
            'country' => $request->country,
            'company' => $request->company ?? null,
            'job_title' => $request->job_title ?? null,
            'price' => $finalAmount, // Use final amount with promo discount
            'payment_method' => 'invoice',
            // 'invoice_number' => $invoiceNumber,
            'file_name' => "{$invoiceNumber}.pdf",
            'file_path' => "invoices/{$invoiceNumber}.pdf",
            'promo_code' => $promoCode,
            'promo_discount_percentage' => $promoDiscountPercentage,
            'promo_discount_amount' => $promoDiscountAmount,
            'success' => true,
        ]);

        // Mark promo code as used if one was applied
        if ($promoCode) {
            $promoCodeModel = \App\Models\PromoCode::where('code', $promoCode)->first();
            if ($promoCodeModel) {
                $promoCodeModel->use();
                Log::info('Promo code marked as used in invoice: ' . $promoCode);
            }
        }

        $this->attachCartItemsToTransaction($transaction, $cart, $user);

        return $transaction;
    }

    public function handleStripePayment($user, $request, $cart)
    {
        \Stripe\Stripe::setApiKey('sk_test_51LxEwJLCJ2NwvyLdsGARtHuQK7fPErHRRA6JvIwejl0HmfDY942Eo5YYWLtOJABhZNGEyRAggPFjfbOpWiFe48oj00gR3aU6OQ');

        // Calculate final amount (use promo code if applied)
        $finalAmount = $request->final_amount ?? $request->subtotal_price;
        $promoCode = $request->promo_code ?? null;
        $promoDiscountPercentage = $request->promo_discount_percentage ?? 0;
        $promoDiscountAmount = $request->promo_discount_amount ?? 0;

        $charge = \Stripe\Charge::create([
            'amount' => intval($finalAmount * 100), // Use final amount with promo discount
            'currency' => 'gbp',
            'description' => 'Course Purchase' . ($promoCode ? " (Promo: {$promoCode})" : ''),
            'source' => $request->stripeToken,
            'receipt_email' => $request->email,
        ]);

        $transaction = $user->transactions()->create([
            'name' => $request->name,
            'email' => $request->email,
            'mobile' => $request->mobile,
            'address1' => $request->address1,
            'address2' => $request->address2,
            'city' => $request->city,
            'zip_code' => $request->zip_code,
            'country' => $request->country,
            'company' => $request->company ?? null,
            'job_title' => $request->job_title ?? null,
            'price' => $finalAmount, // Use final amount with promo discount
            'payment_method' => 'stripe',
            'stripe_charge_id' => $charge->id,
            'promo_code' => $promoCode,
            'promo_discount_percentage' => $promoDiscountPercentage,
            'promo_discount_amount' => $promoDiscountAmount,
            'success' => true,
        ]);

        // Mark promo code as used if one was applied
        if ($promoCode) {
            $promoCodeModel = \App\Models\PromoCode::where('code', $promoCode)->first();
            if ($promoCodeModel) {
                $promoCodeModel->use();
                Log::info('Promo code marked as used in Stripe payment: ' . $promoCode);
            }
        }

        $this->attachCartItemsToTransaction($transaction, $cart, $user);

        return $transaction;
    }

    private function attachCartItemsToTransaction($transaction, $cart, $user)
    {
        foreach ($cart as $item) {
            $transaction->items()->create([
                'model_type' => CourseLocation::class,
                'model_id' => $item->course_location_id,
                'price' => $item->courseLocation->price,
                'quantity' => $item->qty,
                'user_id' => $user->id,
            ]);
        }
    }
}
